# ChessCore 绿地重构计划

## 项目概览

基于对 chesskit-swift 的深入分析和业务需求调研，提出 ChessCore 的完整重构方案。新架构将解决当前的设计问题，优化性能，并为未来扩展奠定基础。

## 当前架构问题分析

### 1. 核心设计问题

#### Move/MetaMove 人为分离
- **问题**：由于 headNode 特殊性导致的不优雅设计
- **影响**：代码复杂度增加，逻辑不够清晰
- **根因**：headNode 作为起始局面缺少 MetaMove 属性

#### 访问权限过严
- **问题**：private 权限导致需要复杂的索引系统
- **影响**：无法直接访问 Node，需要通过整数索引间接操作
- **根因**：过度封装导致的可用性问题

#### 职责重叠
- **问题**：Position 和 Board 之间存在功能重复
- **影响**：代码维护困难，逻辑分散
- **根因**：没有清晰的领域边界

### 2. Undo/Redo 系统过度复杂

#### 多种 UndoState 类型
- **现状**：7 种不同的 UndoState 类型
- **问题**：大量重复代码，维护成本高
- **影响**：每个操作都需要对应的 undo 版本

#### 复杂的路径备份系统
- **现状**：NodePath + 索引的双重备份机制
- **问题**：为了解决索引失效问题引入的复杂性
- **影响**：内存开销大，逻辑复杂

## ChessCore 新架构设计

### 1. 核心设计原则

#### 简化优先
- 消除不必要的复杂性
- 统一数据模型
- 清晰的职责分工

#### 性能优先
- 针对关键场景优化
- 支持懒加载和增量更新
- 优化内存使用

#### 扩展性设计
- 支持未来变体和多引擎
- 模块化设计
- 插件式架构

### 2. 模块化架构

```
ChessCore/
├── Foundation/           # 基础组件
│   ├── Primitives/      # 基本类型 (Square, Piece, Bitboard)
│   ├── Rules/           # 规则引擎 (ChessRules, Validator)
│   └── Utilities/       # 工具类
├── GameTree/            # 游戏树管理
│   ├── Node/            # 节点定义
│   ├── Tree/            # 树结构管理
│   └── Navigation/      # 导航和路径
├── Position/            # 局面管理
│   ├── BoardState/      # 棋盘状态
│   ├── History/         # 历史记录
│   └── Cache/           # 计算缓存
├── Moves/               # 走法系统
│   ├── Move/            # 走法定义
│   ├── Generation/      # 走法生成
│   └── Validation/      # 走法验证
├── Notation/            # 记谱法支持
│   ├── PGN/             # PGN 解析/序列化
│   ├── SAN/             # SAN 解析/转换
│   └── FEN/             # FEN 解析/转换
├── UndoRedo/            # Undo/Redo 系统
│   ├── UndoState/       # Undo 状态定义
│   └── Operations/      # 具体操作的 Undo 支持
└── Extensions/          # 扩展支持 (未来功能)
    ├── Database/        # 数据库支持
    ├── Variants/        # 变体支持
    └── Engine/          # 引擎接口
```

### 3. 核心组件设计

#### 3.1 统一的 Move 系统

```swift
// 统一的 Move 定义，消除 MetaMove 分离
public struct Move: Hashable, Sendable {
    // 核心走法信息
    public let piece: Piece
    public let from: Square
    public let to: Square
    public let moveType: MoveType
    
    // 评价和注释
    public var moveAssessment: Assessment
    public var positionAssessment: Assessment
    public var comments: Comments
    
    // 辅助信息
    public var disambiguation: Disambiguation?
    public var checkState: CheckState
    
    public enum MoveType {
        case normal
        case capture(Piece)
        case castle(CastleType)
        case enPassant(Square)
        case promotion(Piece.Kind)
        case capturePromotion(Piece, Piece.Kind)
    }
}

// 专门的起始局面表示
public struct StartingPosition: Hashable, Sendable {
    public let position: BoardState
    public let metadata: GameMetadata
    public var comments: Comments
}
```

#### 3.2 简化的游戏树系统

```swift
// 开放访问权限，消除索引复杂性
public class GameNode: Identifiable, Sendable {
    public let id: UUID
    public let move: Move?  // nil for root node
    public private(set) weak var parent: GameNode?
    public private(set) var children: [GameNode] = []
    
    // 游戏状态
    public let boardState: BoardState
    public let moveNumber: Int
    public let sideToMove: Piece.Color
    
    // 直接访问，无需索引
    public func addChild(_ move: Move) -> GameNode
    public func removeChild(_ node: GameNode) -> Bool
    public func promoteChild(_ node: GameNode) -> Bool
}

public struct GameTree: Sendable {
    public let root: GameNode
    public let startingPosition: StartingPosition
    
    // 简化的 API，直接操作节点
    public func makeMove(_ move: Move, from node: GameNode) -> (GameNode?, UndoState?)
    public func deleteSubtree(from node: GameNode) -> UndoState?
    public func promoteVariation(_ node: GameNode) -> UndoState?
}
```

#### 3.3 简化的 Undo/Redo 系统

```swift
// 统一的 UndoState 协议
public protocol UndoState: Sendable {
    func undo(in game: Game) throws
    var description: String { get }
}

// 具体的 UndoState 实现
public struct MakeMoveUndoState: UndoState {
    let nodeToRemove: GameNode
    let parentNode: GameNode
    
    public func undo(in game: Game) throws {
        try game.removeNode(nodeToRemove, from: parentNode)
    }
}

public struct DeleteSubtreeUndoState: UndoState {
    let deletedNodes: [GameNode]
    let parentNode: GameNode
    let insertionIndex: Int
    
    public func undo(in game: Game) throws {
        try game.restoreNodes(deletedNodes, to: parentNode, at: insertionIndex)
    }
}

// 游戏操作返回 UndoState，供上层 UndoManager 使用
extension Game {
    public mutating func makeMove(_ move: Move, from node: GameNode) -> (GameNode?, UndoState?) {
        // 实现走法，返回新节点和undo状态
    }
    
    public mutating func deleteSubtree(from node: GameNode) -> UndoState? {
        // 删除子树，返回undo状态
    }
}
```

#### 3.4 高性能局面管理

```swift
// 分层的局面管理
public struct BoardState: Hashable, Sendable {
    // 基础状态 (高频访问)
    private let pieceSet: PieceSet
    private let castlingRights: CastlingRights
    private let enPassant: Square?
    private let halfmoveClock: Int
    
    // 缓存数据 (按需计算)
    private var legalMovesCache: [Piece.Color: [Move]]?
    private var checkStateCache: CheckState?
    private var hashCache: UInt64?
    
    // 高效的查询接口
    public func piece(at square: Square) -> Piece?
    public func legalMoves(for color: Piece.Color) -> [Move]
    public func isInCheck(_ color: Piece.Color) -> Bool
    public func canCastle(_ castle: CastleType) -> Bool
}

// 专门的规则引擎
public struct ChessRules: Sendable {
    public static func generateLegalMoves(from state: BoardState) -> [Move]
    public static func isLegalMove(_ move: Move, in state: BoardState) -> Bool
    public static func applyMove(_ move: Move, to state: BoardState) -> BoardState
    public static func checkState(for color: Piece.Color, in state: BoardState) -> CheckState
}
```

### 4. 关键优化策略

#### 4.1 内存优化

```swift
// 写时复制的局面管理
public struct OptimizedBoardState {
    private var storage: Storage
    
    private mutating func ensureUnique() {
        if !isKnownUniquelyReferenced(&storage) {
            storage = storage.copy()
        }
    }
    
    private class Storage {
        var pieceSet: PieceSet
        var gameState: GameState
        
        func copy() -> Storage {
            Storage(pieceSet: pieceSet, gameState: gameState)
        }
    }
}

// 增量更新支持
public struct IncrementalUpdate {
    public let changes: [BoardChange]
    public let affectedSquares: Set<Square>
    
    public enum BoardChange {
        case addPiece(Piece, Square)
        case removePiece(Square)
        case movePiece(from: Square, to: Square)
    }
}
```

### 5. 未来功能扩展

#### 5.1 数据库支持 (未来功能)

```swift
// 数据库抽象层
public protocol ChessDatabase: Sendable {
    func store(_ game: Game) async throws -> GameID
    func load(_ id: GameID) async throws -> Game
    func search(_ query: GameQuery) async throws -> [GameHeader]
    func sort(_ results: [GameHeader], by criteria: SortCriteria) async throws -> [GameHeader]
    func createIndex(_ field: IndexField) async throws
}

// 排序支持 (与索引联动)
public struct SortCriteria: Sendable {
    let field: SortField
    let order: SortOrder
    let requiresIndex: Bool  // 复杂排序可能需要预先创建索引
    
    public enum SortField {
        case date, rating, moveCount, result
        case custom(String)  // 自定义字段排序
    }
}

// CBV 风格的二进制数据库格式 (未来功能)
public struct ChessBinaryDatabase: ChessDatabase {
    // 高性能二进制格式，类似 ChessBase CBV
    // 支持快速索引、压缩存储、增量更新
}
```

#### 5.2 引擎接口 (未来功能)

```swift
// 引擎通信接口 (基于 UCI 协议)
public protocol ChessEngine: Sendable {
    func startEngine() async throws
    func stopEngine() async throws
    func analyze(_ position: BoardState, depth: Int) async throws -> AnalysisResult
    func getBestMove(_ position: BoardState, timeLimit: TimeInterval) async throws -> Move?
}

// UCI 协议实现
public struct UCIEngine: ChessEngine {
    private let enginePath: String
    private let process: Process
    
    // 标准 UCI 通信，不涉及外部缓存
    // 引擎内部有自己的置换表等优化
}
```

#### 5.3 变体支持 (未来功能)

```swift
// 国际象棋变体支持
public protocol ChessVariant: Sendable {
    func setupStartingPosition() -> BoardState
    func generateLegalMoves(from state: BoardState) -> [Move]
    func isGameOver(_ state: BoardState) -> Bool
}

// Chess960 实现
public struct Chess960: ChessVariant {
    let startingPosition: Int  // 1-960
    
    public func setupStartingPosition() -> BoardState {
        // 根据编号生成 Chess960 起始局面
    }
}
```

## 实施计划

### 阶段一：基础重构 (2-3 周)

#### Week 1: 核心组件
- [ ] 实现统一的 Move 系统
- [ ] 创建 GameNode 和 GameTree
- [ ] 实现基础的 BoardState

#### Week 2: 规则引擎
- [ ] 实现 ChessRules
- [ ] 走法生成和验证
- [ ] 特殊走法处理 (castling, en passant)

#### Week 3: 基础 API
- [ ] 实现基础游戏操作
- [ ] PGN/FEN 解析适配
- [ ] 基础测试覆盖

### 阶段二：高级功能 (2-3 周)

#### Week 4: Undo/Redo 系统
- [ ] 实现统一的 UndoState 架构
- [ ] 各操作的 Undo 支持
- [ ] 与 UndoManager 集成

#### Week 5: 性能优化
- [ ] 实现增量更新
- [ ] 添加计算缓存
- [ ] 内存优化

#### Week 6: 记谱法完善
- [ ] 完善 PGN 解析器
- [ ] SAN/FEN 转换优化
- [ ] 注释系统完善

### 阶段三：集成和优化 (1-2 周)

#### Week 7: 集成测试
- [ ] 与 MacChessBase 集成
- [ ] 性能基准测试
- [ ] 功能完整性验证

#### Week 8: 最终优化
- [ ] 性能调优
- [ ] 文档完善
- [ ] 发布准备

## 预期收益

### 1. 代码质量提升
- **减少代码行数**：预计减少 30-40%
- **降低复杂度**：消除复杂的索引系统和多种 UndoState
- **提高可维护性**：清晰的模块边界和职责分工

### 2. 性能提升
- **内存使用**：优化数据结构，减少 20-30% 内存占用
- **UI 响应**：直接节点访问，提升操作响应速度
- **计算缓存**：减少重复的走法生成和验证

### 3. 开发效率
- **API 简化**：直接节点访问，减少索引操作
- **统一 Undo**：简化的 UndoState 系统
- **模块化**：独立开发和测试各个模块

### 4. 扩展性
- **清晰架构**：为数据库、变体、多引擎等未来功能做好准备
- **插件式设计**：支持功能模块的独立开发
- **性能基础**：为大型数据库操作奠定基础

## 风险评估

### 1. 技术风险
- **迁移复杂性**：需要完全重写，工作量较大
- **功能遗漏**：可能遗漏现有的边缘功能
- **性能回归**：新实现在某些场景下可能性能不如现有版本

### 2. 缓解措施
- **功能对照**：建立完整的功能清单，确保不遗漏
- **性能基准**：建立全面的性能测试套件
- **渐进替换**：先实现核心功能，再逐步完善

## 结论

ChessCore 绿地重构将彻底解决当前架构问题，建立一个清晰、高效、可扩展的国际象棋库。通过消除历史包袱、简化设计、优化性能，新架构将为 MacChessBase 的长期发展提供坚实基础。

重构采用"轻装上阵"的策略，不考虑向后兼容，专注于创建最优的架构设计。未来功能模块的预留设计确保了系统的可扩展性，而简化的 Undo/Redo 系统将与 macOS 原生机制完美集成。